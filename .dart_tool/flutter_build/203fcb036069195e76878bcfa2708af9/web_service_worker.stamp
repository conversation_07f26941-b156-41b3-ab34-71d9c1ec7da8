{"inputs": ["/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/flutter_bootstrap.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/version.json", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/index.html", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/main.dart.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/flutter.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/favicon.png", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/icons/Icon-192.png", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/icons/Icon-maskable-192.png", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/icons/Icon-maskable-512.png", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/icons/Icon-512.png", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/manifest.json", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/AssetManifest.json", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/NOTICES", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/FontManifest.json", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/AssetManifest.bin.json", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/packages/lucide_icons_flutter/assets/lucide.ttf", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/AssetManifest.bin", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/assets/assets/images/README.md", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/skwasm.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/skwasm.js.symbols", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/canvaskit.js.symbols", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/skwasm.wasm", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/chromium/canvaskit.js.symbols", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/chromium/canvaskit.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/chromium/canvaskit.wasm", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/canvaskit.js", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/canvaskit/canvaskit.wasm"], "outputs": ["/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/flutter_service_worker.js"]}