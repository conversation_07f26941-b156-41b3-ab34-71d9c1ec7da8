{"inputs": ["/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/web/*/index.html", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/web/flutter_bootstrap.js", "/opt/homebrew/share/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/*/index.html", "/Users/<USER>/Documents/IDE-VS Code/flutter-mvp-plant-identifier-280725-b/build/web/flutter_bootstrap.js"], "buildKey": "[{\"compileTarget\":\"dart2js\",\"renderer\":\"canvaskit\",\"mainJsPath\":\"main.dart.js\"}]"}