class Plant {
  final int id;
  final String name;
  final String scientificName;
  final String imageUrl;
  final String status;
  final CareRequirements care;
  final DateTime dateAdded;
  final String? category; // Indoor, Outdoor, Flowering
  final double? confidenceLevel;

  Plant({
    required this.id,
    required this.name,
    required this.scientificName,
    required this.imageUrl,
    required this.status,
    required this.care,
    required this.dateAdded,
    this.category,
    this.confidenceLevel,
  });

  factory Plant.fromJson(Map<String, dynamic> json) {
    return Plant(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      scientificName: json['scientificName'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      status: json['status'] ?? 'Healthy',
      care: CareRequirements.fromJson(json['care'] ?? {}),
      dateAdded: DateTime.parse(json['dateAdded'] ?? DateTime.now().toIso8601String()),
      category: json['category'],
      confidenceLevel: json['confidenceLevel']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'scientificName': scientificName,
      'imageUrl': imageUrl,
      'status': status,
      'care': care.toJson(),
      'dateAdded': dateAdded.toIso8601String(),
      'category': category,
      'confidenceLevel': confidenceLevel,
    };
  }

  Plant copyWith({
    int? id,
    String? name,
    String? scientificName,
    String? imageUrl,
    String? status,
    CareRequirements? care,
    DateTime? dateAdded,
    String? category,
    double? confidenceLevel,
  }) {
    return Plant(
      id: id ?? this.id,
      name: name ?? this.name,
      scientificName: scientificName ?? this.scientificName,
      imageUrl: imageUrl ?? this.imageUrl,
      status: status ?? this.status,
      care: care ?? this.care,
      dateAdded: dateAdded ?? this.dateAdded,
      category: category ?? this.category,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
    );
  }
}

class CareRequirements {
  final String light;
  final String water;
  final String temperature;
  final String? humidity;
  final String? fertilizer;

  CareRequirements({
    required this.light,
    required this.water,
    required this.temperature,
    this.humidity,
    this.fertilizer,
  });

  factory CareRequirements.fromJson(Map<String, dynamic> json) {
    return CareRequirements(
      light: json['light'] ?? 'Bright, indirect sunlight',
      water: json['water'] ?? 'Water when soil is dry',
      temperature: json['temperature'] ?? '18-24°C',
      humidity: json['humidity'],
      fertilizer: json['fertilizer'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'light': light,
      'water': water,
      'temperature': temperature,
      'humidity': humidity,
      'fertilizer': fertilizer,
    };
  }
}
