class Diagnosis {
  final List<String> symptoms;
  final String remarks;
  final String? imageUrl;
  final String result;
  final DateTime timestamp;
  final String? severity; // Low, Medium, High
  final List<String>? recommendations;

  Diagnosis({
    required this.symptoms,
    required this.remarks,
    this.imageUrl,
    required this.result,
    required this.timestamp,
    this.severity,
    this.recommendations,
  });

  factory Diagnosis.fromJson(Map<String, dynamic> json) {
    return Diagnosis(
      symptoms: List<String>.from(json['symptoms'] ?? []),
      remarks: json['remarks'] ?? '',
      imageUrl: json['imageUrl'],
      result: json['result'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      severity: json['severity'],
      recommendations: json['recommendations'] != null 
          ? List<String>.from(json['recommendations']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symptoms': symptoms,
      'remarks': remarks,
      'imageUrl': imageUrl,
      'result': result,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity,
      'recommendations': recommendations,
    };
  }
}

class RecentScan {
  final int id;
  final String plantName;
  final String imageUrl;
  final DateTime scanDate;
  final String type; // 'identification' or 'diagnosis'

  RecentScan({
    required this.id,
    required this.plantName,
    required this.imageUrl,
    required this.scanDate,
    required this.type,
  });

  factory RecentScan.fromJson(Map<String, dynamic> json) {
    return RecentScan(
      id: json['id'] ?? 0,
      plantName: json['plantName'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      scanDate: DateTime.parse(json['scanDate'] ?? DateTime.now().toIso8601String()),
      type: json['type'] ?? 'identification',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'plantName': plantName,
      'imageUrl': imageUrl,
      'scanDate': scanDate.toIso8601String(),
      'type': type,
    };
  }
}
