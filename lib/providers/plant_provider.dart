import 'package:flutter/foundation.dart';
import '../models/plant.dart';
import '../models/diagnosis.dart';

class PlantProvider extends ChangeNotifier {
  List<Plant> _myPlants = [];
  List<RecentScan> _recentScans = [];
  bool _isLoading = false;

  // Getters
  List<Plant> get myPlants => _myPlants;
  List<RecentScan> get recentScans => _recentScans;
  bool get isLoading => _isLoading;

  // Filter plants by category
  List<Plant> getPlantsByCategory(String category) {
    if (category == 'All') return _myPlants;
    return _myPlants.where((plant) => plant.category == category).toList();
  }

  // Get plant by ID
  Plant? getPlantById(int id) {
    try {
      return _myPlants.firstWhere((plant) => plant.id == id);
    } catch (e) {
      return null;
    }
  }

  // Add a new plant
  void addPlant(Plant plant) {
    _myPlants.add(plant);
    _addRecentScan(RecentScan(
      id: DateTime.now().millisecondsSinceEpoch,
      plantName: plant.name,
      imageUrl: plant.imageUrl,
      scanDate: DateTime.now(),
      type: 'identification',
    ));
    notifyListeners();
  }

  // Remove a plant
  void removePlant(Plant plant) {
    _myPlants.removeWhere((p) => p.id == plant.id);
    notifyListeners();
  }

  // Update a plant
  void updatePlant(Plant updatedPlant) {
    final index = _myPlants.indexWhere((plant) => plant.id == updatedPlant.id);
    if (index != -1) {
      _myPlants[index] = updatedPlant;
      notifyListeners();
    }
  }

  // Add recent scan
  void _addRecentScan(RecentScan scan) {
    _recentScans.insert(0, scan);
    // Keep only the last 10 scans
    if (_recentScans.length > 10) {
      _recentScans = _recentScans.take(10).toList();
    }
  }

  // Add diagnosis scan
  void addDiagnosisScan(String plantName, String imageUrl) {
    _addRecentScan(RecentScan(
      id: DateTime.now().millisecondsSinceEpoch,
      plantName: plantName,
      imageUrl: imageUrl,
      scanDate: DateTime.now(),
      type: 'diagnosis',
    ));
    notifyListeners();
  }

  // Set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Initialize with sample data
  void initializeSampleData() {
    _myPlants = [
      Plant(
        id: 1,
        name: 'Sunny',
        scientificName: 'Haworthia cooperi',
        imageUrl: 'assets/images/succulent.jpg',
        status: 'Healthy',
        care: CareRequirements(
          light: 'Bright, indirect sunlight',
          water: 'Water sparingly, when soil is dry',
          temperature: '18-24°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 5)),
        category: 'Indoor',
        confidenceLevel: 0.95,
      ),
      Plant(
        id: 2,
        name: 'Rose',
        scientificName: 'Rosa rubiginosa',
        imageUrl: 'assets/images/rose.jpg',
        status: 'Needs Water',
        care: CareRequirements(
          light: 'Full sunlight',
          water: 'Water regularly, keep soil moist',
          temperature: '15-25°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 3)),
        category: 'Outdoor',
        confidenceLevel: 0.88,
      ),
      Plant(
        id: 3,
        name: 'Lily',
        scientificName: 'Lilium candidum',
        imageUrl: 'assets/images/lily.jpg',
        status: 'Healthy',
        care: CareRequirements(
          light: 'Partial shade',
          water: 'Water when top inch of soil is dry',
          temperature: '16-22°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 7)),
        category: 'Flowering',
        confidenceLevel: 0.92,
      ),
      Plant(
        id: 4,
        name: 'Daisy',
        scientificName: 'Bellis perennis',
        imageUrl: 'assets/images/daisy.jpg',
        status: 'Healthy',
        care: CareRequirements(
          light: 'Full sunlight',
          water: 'Water regularly',
          temperature: '10-20°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 2)),
        category: 'Flowering',
        confidenceLevel: 0.90,
      ),
      Plant(
        id: 5,
        name: 'Tulip',
        scientificName: 'Tulipa gesneriana',
        imageUrl: 'assets/images/tulip.jpg',
        status: 'Needs Water',
        care: CareRequirements(
          light: 'Full sunlight',
          water: 'Water when soil surface is dry',
          temperature: '15-20°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 1)),
        category: 'Flowering',
        confidenceLevel: 0.87,
      ),
      Plant(
        id: 6,
        name: 'Orchid',
        scientificName: 'Orchidaceae',
        imageUrl: 'assets/images/orchid.jpg',
        status: 'Healthy',
        care: CareRequirements(
          light: 'Bright, indirect light',
          water: 'Water weekly, allow drainage',
          temperature: '18-25°C',
        ),
        dateAdded: DateTime.now().subtract(const Duration(days: 4)),
        category: 'Indoor',
        confidenceLevel: 0.93,
      ),
    ];

    _recentScans = [
      RecentScan(
        id: 1,
        plantName: 'Fiddle Leaf Fig',
        imageUrl: 'assets/images/fiddle_leaf.jpg',
        scanDate: DateTime.now().subtract(const Duration(hours: 2)),
        type: 'identification',
      ),
      RecentScan(
        id: 2,
        plantName: 'Succulent',
        imageUrl: 'assets/images/succulent_scan.jpg',
        scanDate: DateTime.now().subtract(const Duration(days: 1)),
        type: 'diagnosis',
      ),
    ];

    notifyListeners();
  }
}
