import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/plant.dart';
import '../models/diagnosis.dart';

class OpenRouterService {
  static const String _baseUrl = 'https://openrouter.ai/api/v1/chat/completions';
  static const String _apiKey = '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';
  static const String _model = 'google/gemini-2.5-flash-lite';

  Future<Map<String, dynamic>?> identifyPlant(File imageFile) async {
    try {
      // Convert image to base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      
      final requestBody = {
        "model": _model,
        "messages": [
          {
            "role": "user",
            "content": [
              {
                "type": "text",
                "text": "Identify this plant and provide its common name, scientific name, and basic care instructions (light, water, temperature). Please respond in JSON format with the following structure: {\"name\": \"common name\", \"scientificName\": \"scientific name\", \"confidence\": 0.95, \"care\": {\"light\": \"light requirements\", \"water\": \"water requirements\", \"temperature\": \"temperature range\"}, \"category\": \"Indoor/Outdoor/Flowering\"}"
              },
              {
                "type": "image_url",
                "image_url": {
                  "url": "data:image/jpeg;base64,$base64Image"
                }
              }
            ]
          }
        ]
      };

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        
        // Try to parse JSON from the response
        try {
          final plantData = jsonDecode(content);
          return plantData;
        } catch (e) {
          // If JSON parsing fails, extract information manually
          return _parseTextResponse(content);
        }
      } else {
        throw Exception('Failed to identify plant: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error identifying plant: $e');
    }
  }

  Future<Map<String, dynamic>?> diagnosePlant(
    File imageFile, 
    List<String> symptoms, 
    String remarks
  ) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      
      final symptomsText = symptoms.join(', ');
      
      final requestBody = {
        "model": _model,
        "messages": [
          {
            "role": "user",
            "content": [
              {
                "type": "text",
                "text": "Analyze this plant image for health issues. The user has reported these symptoms: $symptomsText. Additional remarks: $remarks. Please provide a diagnosis and recommendations in JSON format: {\"diagnosis\": \"diagnosis result\", \"severity\": \"Low/Medium/High\", \"recommendations\": [\"recommendation 1\", \"recommendation 2\"], \"confidence\": 0.85}"
              },
              {
                "type": "image_url",
                "image_url": {
                  "url": "data:image/jpeg;base64,$base64Image"
                }
              }
            ]
          }
        ]
      };

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        
        try {
          final diagnosisData = jsonDecode(content);
          return diagnosisData;
        } catch (e) {
          return _parseDiagnosisTextResponse(content);
        }
      } else {
        throw Exception('Failed to diagnose plant: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error diagnosing plant: $e');
    }
  }

  Map<String, dynamic> _parseTextResponse(String content) {
    // Fallback parsing for non-JSON responses
    return {
      'name': 'Unknown Plant',
      'scientificName': 'Species unknown',
      'confidence': 0.5,
      'care': {
        'light': 'Bright, indirect sunlight',
        'water': 'Water when soil is dry',
        'temperature': '18-24°C'
      },
      'category': 'Unknown'
    };
  }

  Map<String, dynamic> _parseDiagnosisTextResponse(String content) {
    return {
      'diagnosis': content,
      'severity': 'Medium',
      'recommendations': ['Monitor plant closely', 'Adjust care routine'],
      'confidence': 0.5
    };
  }
}
