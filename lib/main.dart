import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/plant_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'screens/camera_screen.dart';
import 'screens/identification_result_screen.dart';
import 'screens/my_plants_screen.dart';
import 'screens/diagnose_screen.dart';
import 'screens/profile_screen.dart';
import 'theme.dart';

void main() {
  runApp(const FloraVisionApp());
}

class FloraVisionApp extends StatelessWidget {
  const FloraVisionApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => PlantProvider()),
      ],
      child: MaterialApp(
        title: 'FloraVision',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkTheme,
        home: const SplashScreen(),
        routes: {
          '/home': (context) => const HomeScreen(),
          '/camera': (context) => const CameraScreen(),
          '/my-plants': (context) => const MyPlantsScreen(),
          '/diagnose': (context) => const DiagnoseScreen(),
          '/profile': (context) => const ProfileScreen(),
        },
      ),
    );
  }
}
