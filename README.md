# FloraVision - Plant Identifier App

FloraVision is a Flutter application that allows users to identify plants using their device's camera, diagnose plant health issues, and manage a personal collection of their plants.

## Features

- **Plant Identification**: Use camera or gallery to identify plants with AI
- **Plant Health Diagnosis**: Analyze plant health issues with symptom selection
- **My Plants Collection**: Manage your personal plant collection
- **Care Requirements**: Get detailed care instructions for identified plants
- **Recent Scans**: Track your identification and diagnosis history
- **Dark Theme**: Modern, dark-themed UI design

## Screenshots

The app includes the following screens:
- Splash Screen with animated logo
- Home Screen with action buttons and plant collections
- Camera Screen with real-time preview and capture
- Identification Result Screen with plant details and care requirements
- My Plants Screen with filterable plant grid
- Diagnose Screen with symptom selection and photo upload
- Profile Screen with user settings

## Technical Details

### Architecture
- **State Management**: Provider pattern
- **API Integration**: OpenRouter API for plant identification
- **Camera**: Flutter camera plugin for image capture
- **Image Handling**: Image picker for gallery selection

### Dependencies
- `provider`: State management
- `http`: API calls
- `image_picker`: Image selection from gallery
- `camera`: Camera functionality
- `lucide_flutter`: Modern icons

### API Integration
The app uses OpenRouter API with Google's Gemini model for:
- Plant identification from images
- Plant health diagnosis
- Care requirement recommendations

## Setup Instructions

1. **Install Flutter**: Make sure Flutter SDK is installed
2. **Get Dependencies**: Run `flutter pub get`
3. **Add Assets**: Add a logo.png file to `assets/images/`
4. **Run the App**: Use `flutter run` to start the application

## Project Structure

```
lib/
├── api/
│   └── openrouter_service.dart     # API service for plant identification
├── models/
│   ├── plant.dart                  # Plant data model
│   └── diagnosis.dart              # Diagnosis data model
├── providers/
│   └── plant_provider.dart         # State management
├── screens/
│   ├── splash_screen.dart          # App launch screen
│   ├── home_screen.dart            # Main dashboard
│   ├── camera_screen.dart          # Camera capture
│   ├── identification_result_screen.dart  # Results display
│   ├── my_plants_screen.dart       # Plant collection
│   ├── diagnose_screen.dart        # Health diagnosis
│   └── profile_screen.dart         # User profile
├── widgets/
│   ├── bottom_nav_bar.dart         # Navigation bar
│   ├── plant_card.dart             # Plant display card
│   └── recent_scan_card.dart       # Recent scan item
├── theme.dart                      # App theme configuration
└── main.dart                       # App entry point
```

## Features Implementation Status

✅ **Completed**:
- Project structure and dependencies
- Dark theme implementation
- All screen layouts and navigation
- State management with Provider
- API service integration
- Camera functionality
- Plant identification flow
- Plant collection management
- Health diagnosis feature
- User profile and settings

## Notes

- The app includes sample data for demonstration
- Camera permissions need to be configured for each platform
- API key is included for testing (should be secured in production)
- Images are stored locally (cloud storage can be added later)

## Future Enhancements

- Cloud storage for plant images
- Push notifications for plant care reminders
- Social features for sharing plants
- Advanced plant care tracking
- Offline plant identification
- Multiple language support
